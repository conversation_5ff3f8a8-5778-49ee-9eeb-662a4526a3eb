#!/bin/bash

# FaceTrace Backend API Deployment Script for Google Cloud Run
# This script deploys only the backend API components to Google Cloud

set -e

# Configuration
PROJECT_ID="realestate-2b2bf"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Starting FaceTrace Backend API deployment to Google Cloud Run..."

# Step 1: Authenticate and set project
echo "📋 Step 1: Setting up Google Cloud authentication..."
echo "🔍 Current project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
echo "🔍 Current account: $(gcloud config get-value account 2>/dev/null || echo 'Not logged in')"

# Check if already authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 Please authenticate with Google Cloud..."
    gcloud auth login
else
    echo "✅ Already authenticated"
fi

gcloud config set project $PROJECT_ID
echo "✅ Project set to: $PROJECT_ID"

echo "🔧 Enabling required APIs..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Step 2: Build and push Docker image
echo "🐳 Step 2: Building Docker image for backend API..."
if [ ! -f "Dockerfile.backend" ]; then
    echo "❌ Error: Dockerfile.backend not found!"
    echo "📝 Please ensure all deployment files are in the current directory"
    exit 1
fi

echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME -f Dockerfile.backend . || {
    echo "❌ Docker build failed!"
    echo "📝 Please check your Docker installation and try again"
    exit 1
}

echo "📤 Pushing image to Google Container Registry..."
docker push $IMAGE_NAME || {
    echo "❌ Docker push failed!"
    echo "📝 Please check your authentication and try again"
    exit 1
}

# Step 3: Deploy to Cloud Run
echo "☁️ Step 3: Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
  --image $IMAGE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 1000 \
  --max-instances 10 \
  --min-instances 0 \
  --project $PROJECT_ID \
  --set-env-vars NODE_ENV=production,NEXT_PUBLIC_DISABLE_AUTH=true,NEXT_PUBLIC_DISABLE_PAYMENT=true || {
    echo "❌ Cloud Run deployment failed!"
    echo "📝 Please check the error messages above and try again"
    exit 1
}

echo "✅ Deployment completed!"
echo "🌐 Your backend API is available at:"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo $SERVICE_URL

echo ""
echo "📝 Next steps:"
echo "1. Run: chmod +x setup-env-vars.sh && ./setup-env-vars.sh"
echo "2. Run: chmod +x test-backend-api.sh && ./test-backend-api.sh"
echo "3. Configure your frontend to use: $SERVICE_URL"
echo "4. Set up custom domain (optional)"
echo ""
echo "🔧 Important: Update your frontend environment variables:"
echo "NEXT_PUBLIC_API_BASE_URL=$SERVICE_URL"
